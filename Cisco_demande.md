hook.js:608 Erreur lors de la synchronisation Firebase vers Google: FirebaseError: No document to update: projects/florasynth-a461d/databases/(default)/documents/users/nTfdEgyg0gP4GK2TnAOKNnI6flZ2/calendar_settings/settings
overrideMethod @ hook.js:608
hook.js:608 Erreur lors de la synchronisation complète: FirebaseError: No document to update: projects/florasynth-a461d/databases/(default)/documents/users/nTfdEgyg0gP4GK2TnAOKNnI6flZ2/calendar_settings/settings
overrideMethod @ hook.js:608
firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel?VER=8&database=projects%2Fflorasynth-a461d%2Fdatabases%2F(default)&gsessionid=LOUg_bKt5cV0Xf_rHJoRyoefisTQCC0xBg-9wzloCu8&SID=gDRbHQEGepn0Uxs9wleyIw&RID=83592&TYPE=terminate&zx=hjnho4okuzwl:1  Failed to load resource: the server responded with a status of 400 ()
