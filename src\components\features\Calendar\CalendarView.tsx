import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { CalendarEvent, CalendarViewOptions, TreatmentType } from '@/types/calendar';
import { getCalendarEvents } from '@/services/api';
import { calendarSyncService } from '@/services/calendarSync';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Spinner } from '@/components/common/Spinner';
import { CalendarIcon, SyncIcon, PlusIcon, FilterIcon } from '@/components/common/icons';

/**
 * Composant principal de vue du calendrier
 * Affiche les événements de traitement des plantes avec différentes vues (mois, semaine, liste)
 */
const CalendarView: React.FC = () => {
  const { user } = useAuth();
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [viewOptions, setViewOptions] = useState<CalendarViewOptions>({
    viewType: 'month',
    currentDate: new Date(),
    filters: {},
    showCompleted: false,
    showOverdue: true
  });
  const [syncMessage, setSyncMessage] = useState<string>('');

  // Chargement des événements
  useEffect(() => {
    if (!user) return;

    const unsubscribe = getCalendarEvents(user.uid, (fetchedEvents) => {
      setEvents(fetchedEvents);
      setIsLoading(false);
    }, viewOptions.filters);

    return () => unsubscribe();
  }, [user, viewOptions.filters]);

  // Synchronisation avec Google Calendar
  const handleSync = async () => {
    if (!user || isSyncing) return;

    setIsSyncing(true);
    setSyncMessage('');

    try {
      const result = await calendarSyncService.fullSync(user.uid);
      
      if (result.success) {
        setSyncMessage(`Synchronisation réussie: ${result.syncedEvents} événements synchronisés`);
      } else {
        setSyncMessage(`Erreur de synchronisation: ${result.message}`);
      }
    } catch (error) {
      console.error('Erreur lors de la synchronisation:', error);
      setSyncMessage('Erreur lors de la synchronisation');
    } finally {
      setIsSyncing(false);
      // Effacer le message après 5 secondes
      setTimeout(() => setSyncMessage(''), 5000);
    }
  };

  // Filtrage des événements selon les options de vue
  const filteredEvents = events.filter(event => {
    const eventDate = event.startDate;
    const now = new Date();

    // Filtrer par statut
    if (!viewOptions.showCompleted && event.status === 'completed') {
      return false;
    }
    if (!viewOptions.showOverdue && event.status === 'overdue') {
      return false;
    }

    // Filtrer par période selon le type de vue
    switch (viewOptions.viewType) {
      case 'day':
        return eventDate.toDateString() === viewOptions.currentDate.toDateString();
      case 'week':
        const weekStart = new Date(viewOptions.currentDate);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekEnd.getDate() + 6);
        return eventDate >= weekStart && eventDate <= weekEnd;
      case 'month':
        return eventDate.getMonth() === viewOptions.currentDate.getMonth() &&
               eventDate.getFullYear() === viewOptions.currentDate.getFullYear();
      default:
        return true;
    }
  });

  // Couleurs par type de traitement
  const getTreatmentColor = (type: TreatmentType): string => {
    const colors = {
      fertilisation: 'bg-green-100 text-green-800',
      traitement: 'bg-red-100 text-red-800',
      arrosage: 'bg-blue-100 text-blue-800',
      rempotage: 'bg-purple-100 text-purple-800',
      taille: 'bg-orange-100 text-orange-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  // Couleurs par statut
  const getStatusColor = (status: string): string => {
    const colors = {
      scheduled: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      overdue: 'bg-red-100 text-red-800',
      skipped: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  // Navigation entre les périodes
  const navigatePeriod = (direction: 'prev' | 'next') => {
    const newDate = new Date(viewOptions.currentDate);
    
    switch (viewOptions.viewType) {
      case 'day':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
        break;
      case 'week':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case 'month':
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
    }
    
    setViewOptions(prev => ({ ...prev, currentDate: newDate }));
  };

  // Formatage de la date pour l'affichage
  const formatDateHeader = (): string => {
    const date = viewOptions.currentDate;
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long'
    };

    switch (viewOptions.viewType) {
      case 'day':
        return date.toLocaleDateString('fr-FR', { 
          weekday: 'long', 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        });
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekEnd.getDate() + 6);
        return `${weekStart.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' })} - ${weekEnd.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short', year: 'numeric' })}`;
      case 'month':
        return date.toLocaleDateString('fr-FR', options);
      default:
        return 'Tous les événements';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête avec contrôles */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-6 w-6 text-green-600" />
          <h1 className="text-2xl font-bold text-gray-900">Calendrier des Traitements</h1>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            onClick={handleSync}
            disabled={isSyncing}
            variant="outline"
            size="sm"
          >
            <SyncIcon className={`h-4 w-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
            {isSyncing ? 'Synchronisation...' : 'Synchroniser'}
          </Button>
          
          <Button size="sm">
            <PlusIcon className="h-4 w-4 mr-2" />
            Nouvel événement
          </Button>
        </div>
      </div>

      {/* Message de synchronisation */}
      {syncMessage && (
        <div className={`p-3 rounded-md ${syncMessage.includes('Erreur') ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
          {syncMessage}
        </div>
      )}

      {/* Contrôles de vue */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigatePeriod('prev')}
              >
                ←
              </Button>
              <h2 className="text-lg font-semibold">{formatDateHeader()}</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigatePeriod('next')}
              >
                →
              </Button>
            </div>
            
            <div className="flex items-center gap-2">
              {(['day', 'week', 'month', 'list'] as const).map((view) => (
                <Button
                  key={view}
                  variant={viewOptions.viewType === view ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewOptions(prev => ({ ...prev, viewType: view }))}
                >
                  {view === 'day' ? 'Jour' : 
                   view === 'week' ? 'Semaine' : 
                   view === 'month' ? 'Mois' : 'Liste'}
                </Button>
              ))}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {filteredEvents.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <CalendarIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Aucun événement pour cette période</p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredEvents.map((event) => (
                <div
                  key={event.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-medium text-gray-900">{event.title}</h3>
                      <Badge className={getTreatmentColor(event.treatmentType)}>
                        {event.treatmentType}
                      </Badge>
                      <Badge className={getStatusColor(event.status)}>
                        {event.status === 'scheduled' ? 'Programmé' :
                         event.status === 'completed' ? 'Terminé' :
                         event.status === 'overdue' ? 'En retard' : 'Ignoré'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-1">{event.plantName}</p>
                    <p className="text-sm text-gray-500">
                      {event.startDate.toLocaleDateString('fr-FR', {
                        weekday: 'short',
                        day: 'numeric',
                        month: 'short',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {event.isRecurring && (
                      <Badge variant="outline">Récurrent</Badge>
                    )}
                    <Button variant="ghost" size="sm">
                      Modifier
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CalendarView;
